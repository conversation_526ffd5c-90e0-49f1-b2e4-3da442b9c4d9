{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "BusinessCentral": {"BaseUri": "https://api.businesscentral.dynamics.com/v2.0/{te}/YOUR_ENVIRONMENT_NAME/ODataV4/", "CompanyId": "YOUR_BC_COMPANY_ID_GUID", "Username": "MCP_INTEGRATION", "WebServiceAccessKey": "YOUR_BC_USER_WEB_SERVICE_ACCESS_KEY", "ApiBaseUri": "https://api.businesscentral.dynamics.com/v2.0", "ClientId": "5502b2e3-330a-48c3-b497-7268d4d90142", "ClientSecret": "****************************************", "Scope": "https://api.businesscentral.dynamics.com/.default", "EnvironmentName": "Aleta-Production", "Api": "api/hougaard/SOD/v2.0/$metadata#companies(384f301c-eb7a-ed11-998b-000d3a251a0f)/inrebusItems"}, "MCP_Server": {"ApiKey": "test123"}}