{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "BusinessCentral": {"BaseUri": "https://api.businesscentral.dynamics.com/v2.0/{te}/YOUR_ENVIRONMENT_NAME/ODataV4/", "CompanyId": "YOUR_BC_COMPANY_ID_GUID", "Username": "MCP_INTEGRATION", "WebServiceAccessKey": "YOUR_BC_USER_WEB_SERVICE_ACCESS_KEY", "ApiBaseUri": "https://api.businesscentral.dynamics.com/v2.0", "ClientId": "5502b2e3-330a-48c3-b497-7268d4d90142", "ClientSecret": "****************************************", "Scope": "https://api.businesscentral.dynamics.com/.default", "EnvironmentName": "Aleta-Production", "Api": "api/hougaard/SOD/v2.0/$metadata#companies(384f301c-eb7a-ed11-998b-000d3a251a0f)/inrebusItems"}, "Elasticsearch": {"AuthUrl": "https://auth.aleta.hr/token", "ApiUrl": "https://ofnpbmw9cl.execute-api.eu-central-1.amazonaws.com/production/productContext", "ClientId": "359bufj2vc0u7tjfmjel3fqri3", "ClientSecret": "1sa9s69poo1jhh0ola4ldu9j37b0nobvgm9pgn6450ha6bhrhtvb", "Scope": "https://aleta-auth.aleta.hr/admin", "XsrfToken": "bce9d383-eb25-48a4-84e0-0f92c0955c78"}, "MCP_Server": {"ApiKey": "test123"}}