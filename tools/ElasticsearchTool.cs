using System.ComponentModel;
using ModelContextProtocol.Server;
using System.Text.Json;
using Business_Central_MCP_Server.services;

namespace Business_Central_MCP_Server.tools;

[McpServerToolType]
public class ElasticsearchTool
{
    private readonly ElasticsearchService _elasticsearchService;

    public ElasticsearchTool(ElasticsearchService elasticsearchService)
    {
        _elasticsearchService = elasticsearchService;
    }

    [McpServerTool, Description("Get product information by product IDs from Elasticsearch API. Returns detailed product data including name, description, price, inventory, and technical specifications.")]
    public async Task<string> GetProductsByIds(
        [Description("Array of product IDs to search for")] string[] productIds,
        [Description("Output format: 'json' or 'md' (default: json)")] string outputFormat = "json")
    {
        try
        {
            var response = await _elasticsearchService.GetProductsByIdsAsync(productIds, outputFormat);
            return JsonSerializer.Serialize(response, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
        }
        catch (Exception ex)
        {
            return JsonSerializer.Serialize(new 
            { 
                success = false, 
                error = ex.Message 
            });
        }
    }

    [McpServerTool, Description("Get a simplified list of products by IDs, returning only the product array without the wrapper response structure.")]
    public async Task<string> GetProductsList(
        [Description("Array of product IDs to search for")] string[] productIds,
        [Description("Output format: 'json' or 'md' (default: json)")] string outputFormat = "json")
    {
        try
        {
            var products = await _elasticsearchService.GetProductsListAsync(productIds, outputFormat);
            return JsonSerializer.Serialize(products, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
        }
        catch (Exception ex)
        {
            return JsonSerializer.Serialize(new 
            { 
                success = false, 
                error = ex.Message 
            });
        }
    }

    [McpServerTool, Description("Get product information for a single product ID from Elasticsearch API.")]
    public async Task<string> GetSingleProduct(
        [Description("Single product ID to search for")] string productId,
        [Description("Output format: 'json' or 'md' (default: json)")] string outputFormat = "json")
    {
        try
        {
            var products = await _elasticsearchService.GetProductsListAsync(new[] { productId }, outputFormat);
            var product = products.FirstOrDefault();
            
            if (product == null)
            {
                return JsonSerializer.Serialize(new 
                { 
                    success = false, 
                    error = "Product not found" 
                });
            }

            return JsonSerializer.Serialize(product, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
        }
        catch (Exception ex)
        {
            return JsonSerializer.Serialize(new 
            { 
                success = false, 
                error = ex.Message 
            });
        }
    }
}
