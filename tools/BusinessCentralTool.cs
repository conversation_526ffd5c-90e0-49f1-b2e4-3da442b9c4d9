using System.ComponentModel;
using ModelContextProtocol.Server;
using System.Text.Json;

namespace Business_Central_MCP_Server.tools;

[McpServerToolType]
public class BusinessCentralTool
{
    private BusinessCentralService businessCentralService;
    public BusinessCentralTool(BusinessCentralService businessCentralService)
    {
        this.businessCentralService = businessCentralService;
    }

    [McpServerTool, Description("Get a list of inrebus items.")]
    public async Task<string> GetInrebusItems()
    {
        var items = await businessCentralService.GetItemsAsync();
        return JsonSerializer.Serialize(items);
    }
    
    [McpServerTool, Description("Get a specific item by filter no")]
    public async Task<string> GetInrebusItemByFilter(string no)
    {
        var items = await businessCentralService.GetItemByNoAsync(no);
        return JsonSerializer.Serialize(items);
    }
}