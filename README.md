# Business Central MCP Server

A Model Context Protocol server implementation for Microsoft Business Central integration, built with .NET 8.

## Overview

This server provides a bridge between AI assistants and Business Central data using the Model Context Protocol (MCP). It exposes Business Central data through MCP tools that can be called by AI assistants.

## Features

- MCP server with stdio transport
- Business Central API integration using OAuth authentication
- Tools for retrieving Business Central items
- RESTful API endpoints for direct HTTP access

## Prerequisites

- .NET 8.0 SDK
- Microsoft Business Central account with API access
- Azure AD application registration for authentication

## Configuration

Update the `appsettings.json` file with your Business Central credentials:

```json
"BusinessCentral": {
  "TenantId": "your-tenant-id",
  "ClientId": "your-client-id",
  "ClientSecret": "your-client-secret",
  "Scope": "https://api.businesscentral.dynamics.com/.default",
  "EnvironmentName": "your-environment-name"
}
```

## Running the Server

```bash
dotnet run
```

The server will start and listen for MCP requests via standard input/output.

## Available MCP Tools

- `Echo`: Simple echo tool for testing
- `ReverseEcho`: Reverses and returns the input string
- `GetInrebusItems`: Retrieves a list of items from Business Central
- `GetInrebusItemByFilter`: Retrieves specific items by filter

## API Endpoints

- `GET /mcp/items`: Retrieves Business Central items (requires API key)

## License

copyright Aleta