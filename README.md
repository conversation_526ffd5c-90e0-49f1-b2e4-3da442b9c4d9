# Business Central MCP Server

A Model Context Protocol server implementation for Microsoft Business Central integration, built with .NET 8.

## Overview

This server provides a bridge between AI assistants and Business Central data using the Model Context Protocol (MCP). It exposes Business Central data through MCP tools that can be called by AI assistants.

## Features

- MCP server with stdio transport
- Business Central API integration using OAuth authentication
- Elasticsearch API integration with client credentials authentication
- Tools for retrieving Business Central items
- Tools for searching products via Elasticsearch API
- RESTful API endpoints for direct HTTP access

## Prerequisites

- .NET 8.0 SDK
- Microsoft Business Central account with API access
- Azure AD application registration for authentication

## Configuration

Update the `appsettings.json` file with your Business Central and Elasticsearch credentials:

```json
"BusinessCentral": {
  "TenantId": "your-tenant-id",
  "ClientId": "your-client-id",
  "ClientSecret": "your-client-secret",
  "Scope": "https://api.businesscentral.dynamics.com/.default",
  "EnvironmentName": "your-environment-name"
},
"Elasticsearch": {
  "AuthUrl": "https://auth.aleta.hr/token",
  "ApiUrl": "https://ofnpbmw9cl.execute-api.eu-central-1.amazonaws.com/production/productContext",
  "ClientId": "your-elasticsearch-client-id",
  "ClientSecret": "your-elasticsearch-client-secret",
  "Scope": "https://aleta-auth.aleta.hr/admin",
  "XsrfToken": "your-xsrf-token"
}
```

## Running the Server

```bash
dotnet run
```

The server will start and listen for MCP requests via standard input/output.

## Available MCP Tools

### Business Central Tools
- `GetInrebusItems`: Retrieves a list of items from Business Central
- `GetInrebusItemByFilter`: Retrieves specific items by filter

### Elasticsearch Tools
- `GetProductsByIds`: Get detailed product information by product IDs from Elasticsearch API
- `GetProductsList`: Get a simplified list of products by IDs (returns only the product array)
- `GetSingleProduct`: Get product information for a single product ID

### Utility Tools
- `Echo`: Simple echo tool for testing
- `ReverseEcho`: Reverses and returns the input string

## API Endpoints

- `GET /mcp/items`: Retrieves Business Central items (requires API key)
- `POST /mcp/products`: Search products via Elasticsearch API (requires API key)
  - Request body: `{ "productIds": ["1578581", "1010194"], "outputFormat": "json" }`

## License

copyright Aleta