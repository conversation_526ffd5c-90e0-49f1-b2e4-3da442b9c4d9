I want to build Elastic search MCP. Elastic search api is hosted on aws.

here is example of one request for elastic search:

[POST] https://ofnpbmw9cl.execute-api.eu-central-1.amazonaws.com/production/productContext

body:
{
"productids": ["1578581","1010194", "1016132"],
"output": "json" // or md
}

Headers: Bearer token

Auth:

[POST] https://auth.aleta.hr/token

body x-www-form-urlencoded:
contetType: application/x-www-form-urlencoded
grant_type: client_credentials
scope: https://aleta-auth.aleta.hr/admin


header:
cookie: XSRF-TOKEN=bce9d383-eb25-48a4-84e0-0f92c0955c78

auth:
basic
username: 359bufj2vc0u7tjfmjel3fqri3
password:1sa9s69poo1jhh0ola4ldu9j37b0nobvgm9pgn6450ha6bhrhtvb

response:

{
"success": true,
"data": {
"productids": [
{
"id": "1016132",
"name": "PL6-B16/1",
"description": "Automatski osigurac B16 1-polni 6 kA",
"mpn": "286521",
"unit": "KOM",
"price": 4.16,
"ean": "4015082865214",
"moq": 0,
"manufacturerId": "EATON",
"manufacturerName": "Eaton (Moeller)",
"productSeriesName": "xPole",
"inventoryZagreb": 1293,
"inventoryZadar": 12,
"image": "https://assets.aleta.hr/150x150/b7543aec-0a1b-4adc-ba2d-cbf6afe6b0d8_optimized.webp",
"datasheets": [
"https://assets.aleta.hr/811ccb6d-889d-4331-93a9-4623d39125bb.pdf",
"https://assets.aleta.hr/ece34e16-7c8d-439f-945d-dcea372d595d.pdf"
],
"etimClass": {
"etimClassId": "EC000042",
"etimClassName": "Automatski osigurač"
}
}
]
}
}

so basically as I understand, this is not correct info for elastic search MCP. this more like api access that presents elastic search results right?
So for now make functionality with existing API wrapper and not actual elastic search details. You can just add this additional functionality to this existing mcp