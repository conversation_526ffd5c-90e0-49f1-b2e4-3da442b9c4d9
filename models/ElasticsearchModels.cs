using System.Text.Json.Serialization;

namespace Business_Central_MCP_Server.models;

public class ElasticsearchAuthRequest
{
    [JsonPropertyName("grant_type")]
    public string GrantType { get; set; } = "client_credentials";
    
    [JsonPropertyName("scope")]
    public string Scope { get; set; } = string.Empty;
}

public class ElasticsearchAuthResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } = string.Empty;
    
    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } = string.Empty;
    
    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }
}

public class ElasticsearchProductRequest
{
    [JsonPropertyName("productids")]
    public string[] ProductIds { get; set; } = Array.Empty<string>();
    
    [JsonPropertyName("output")]
    public string Output { get; set; } = "json";
}

public class ElasticsearchEtimClass
{
    [JsonPropertyName("etimClassId")]
    public string EtimClassId { get; set; } = string.Empty;
    
    [JsonPropertyName("etimClassName")]
    public string EtimClassName { get; set; } = string.Empty;
}

public class ElasticsearchProduct
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;
    
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
    
    [JsonPropertyName("mpn")]
    public string Mpn { get; set; } = string.Empty;
    
    [JsonPropertyName("unit")]
    public string Unit { get; set; } = string.Empty;
    
    [JsonPropertyName("price")]
    public decimal Price { get; set; }
    
    [JsonPropertyName("ean")]
    public string Ean { get; set; } = string.Empty;
    
    [JsonPropertyName("moq")]
    public int Moq { get; set; }
    
    [JsonPropertyName("manufacturerId")]
    public string ManufacturerId { get; set; } = string.Empty;
    
    [JsonPropertyName("manufacturerName")]
    public string ManufacturerName { get; set; } = string.Empty;
    
    [JsonPropertyName("productSeriesName")]
    public string ProductSeriesName { get; set; } = string.Empty;
    
    [JsonPropertyName("inventoryZagreb")]
    public int InventoryZagreb { get; set; }
    
    [JsonPropertyName("inventoryZadar")]
    public int InventoryZadar { get; set; }
    
    [JsonPropertyName("image")]
    public string Image { get; set; } = string.Empty;
    
    [JsonPropertyName("datasheets")]
    public string[] Datasheets { get; set; } = Array.Empty<string>();
    
    [JsonPropertyName("etimClass")]
    public ElasticsearchEtimClass? EtimClass { get; set; }
}

public class ElasticsearchProductData
{
    [JsonPropertyName("productids")]
    public ElasticsearchProduct[] Products { get; set; } = Array.Empty<ElasticsearchProduct>();
}

public class ElasticsearchProductResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }
    
    [JsonPropertyName("data")]
    public ElasticsearchProductData? Data { get; set; }
}
