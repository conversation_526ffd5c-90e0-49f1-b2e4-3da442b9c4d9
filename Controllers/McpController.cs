using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

[ApiController]
[Route("mcp")]
public class McpController : ControllerBase
{
    private readonly BusinessCentralService _service;
    private readonly IConfiguration _config;

    public McpController(BusinessCentralService service, IConfiguration config)
    {
        _service = service;
        _config = config;
    }

    [HttpGet("items")]
    public async Task<IActionResult> GetItems([FromHeader(Name = "x-api-key")] string apiKey)
    {
        var expectedApiKey = _config["MCP_Server:ApiKey"];
        if (string.IsNullOrEmpty(apiKey) || apiKey != expectedApiKey)
        {
            return Unauthorized(new { error = "Invalid API Key" });
        }

        var items = await _service.GetItemsAsync();
        return Ok(new { value = items });
    }
}
