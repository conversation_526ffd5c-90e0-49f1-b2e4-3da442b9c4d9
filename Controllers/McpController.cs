using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Business_Central_MCP_Server.services;

[ApiController]
[Route("mcp")]
public class McpController : ControllerBase
{
    private readonly BusinessCentralService _service;
    private readonly ElasticsearchService _elasticsearchService;
    private readonly IConfiguration _config;

    public McpController(BusinessCentralService service, ElasticsearchService elasticsearchService, IConfiguration config)
    {
        _service = service;
        _elasticsearchService = elasticsearchService;
        _config = config;
    }

    [HttpGet("items")]
    public async Task<IActionResult> GetItems([FromHeader(Name = "x-api-key")] string apiKey)
    {
        var expectedApiKey = _config["MCP_Server:ApiKey"];
        if (string.IsNullOrEmpty(apiKey) || apiKey != expectedApiKey)
        {
            return Unauthorized(new { error = "Invalid API Key" });
        }

        var items = await _service.GetItemsAsync();
        return Ok(new { value = items });
    }

    [HttpPost("products")]
    public async Task<IActionResult> GetProducts(
        [FromHeader(Name = "x-api-key")] string apiKey,
        [FromBody] ProductSearchRequest request)
    {
        var expectedApiKey = _config["MCP_Server:ApiKey"];
        if (string.IsNullOrEmpty(apiKey) || apiKey != expectedApiKey)
        {
            return Unauthorized(new { error = "Invalid API Key" });
        }

        if (request?.ProductIds == null || request.ProductIds.Length == 0)
        {
            return BadRequest(new { error = "Product IDs are required" });
        }

        try
        {
            var response = await _elasticsearchService.GetProductsByIdsAsync(
                request.ProductIds,
                request.OutputFormat ?? "json");
            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = ex.Message });
        }
    }
}

public class ProductSearchRequest
{
    public string[] ProductIds { get; set; } = Array.Empty<string>();
    public string? OutputFormat { get; set; } = "json";
}
