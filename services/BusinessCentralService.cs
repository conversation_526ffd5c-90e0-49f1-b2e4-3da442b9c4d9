using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Client;
using System.Net.Http.Headers;
using System.Text.Json;

public class BusinessCentralService
{
    private readonly IConfiguration _config;
    private readonly IHttpClientFactory _httpClientFactory;

    public BusinessCentralService(IConfiguration config, IHttpClientFactory httpClientFactory)
    {
        _config = config;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<string> GetAccessTokenAsync()
    {
        var tenantId = _config["BusinessCentral:TenantId"];
        var clientId = _config["BusinessCentral:ClientId"];
        var clientSecret = _config["BusinessCentral:ClientSecret"];
        var scope = _config["BusinessCentral:Scope"];

        var app = ConfidentialClientApplicationBuilder.Create(clientId)
            .WithClientSecret(clientSecret)
            .WithAuthority($"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token")
            .Build();

        var result = await app.AcquireTokenForClient(new[] { scope }).ExecuteAsync();
        return result.AccessToken;
    }

    public async Task<List<BusinessCentralItem>> GetItemsAsync()
    {
        var token = await GetAccessTokenAsync();
        // var apiUrl = _config["BusinessCentral:ApiBaseUri"] + "/" + _config["BusinessCentral:TenantId"] + "/" + _config["BusinessCentral:EnvironmentName"] + "/" + _config["BusinessCentral:Api"] ;
        var apiUrl = "https://api.businesscentral.dynamics.com/v2.0/c9c90ee4-619e-4f77-a890-c5fc55d1b9b6/Aleta-Production/api/hougaard/SOD/v2.0/companies(384f301c-eb7a-ed11-998b-000d3a251a0f)/inrebusItems" ;
        var client = _httpClientFactory.CreateClient();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var response = await client.GetAsync(apiUrl);
        response.EnsureSuccessStatusCode();

        var json = await response.Content.ReadAsStringAsync();
        using var doc = JsonDocument.Parse(json);
        var items = doc.RootElement.GetProperty("value").Deserialize<List<BusinessCentralItem>>();
        return items.Take(50).ToList();
    }
    
    public async Task<List<BusinessCentralItem>> GetItemByNoAsync(string no)
    {
        var token = await GetAccessTokenAsync();
        // var apiUrl = _config["BusinessCentral:ApiBaseUri"] + "/" + _config["BusinessCentral:TenantId"] + "/" + _config["BusinessCentral:EnvironmentName"] + "/" + _config["BusinessCentral:Api"] ;
        var apiUrl = $"https://api.businesscentral.dynamics.com/v2.0/c9c90ee4-619e-4f77-a890-c5fc55d1b9b6/Aleta-Production/api/hougaard/SOD/v2.0/companies(384f301c-eb7a-ed11-998b-000d3a251a0f)/inrebusItems?$filter=no_ eq '{no}'" ;
        var client = _httpClientFactory.CreateClient();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var response = await client.GetAsync(apiUrl);
        response.EnsureSuccessStatusCode();

        var json = await response.Content.ReadAsStringAsync();
        using var doc = JsonDocument.Parse(json);
        var items = doc.RootElement.GetProperty("value").Deserialize<List<BusinessCentralItem>>();
        return items;
    }
}