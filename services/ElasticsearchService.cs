using Microsoft.Extensions.Configuration;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Business_Central_MCP_Server.models;

namespace Business_Central_MCP_Server.services;

public class ElasticsearchService
{
    private readonly IConfiguration _config;
    private readonly IHttpClientFactory _httpClientFactory;
    private string? _cachedAccessToken;
    private DateTime _tokenExpiry = DateTime.MinValue;

    public ElasticsearchService(IConfiguration config, IHttpClientFactory httpClientFactory)
    {
        _config = config;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<string> GetAccessTokenAsync()
    {
        // Check if we have a valid cached token
        if (!string.IsNullOrEmpty(_cachedAccessToken) && DateTime.UtcNow < _tokenExpiry)
        {
            return _cachedAccessToken;
        }

        var authUrl = _config["Elasticsearch:AuthUrl"];
        var clientId = _config["Elasticsearch:ClientId"];
        var clientSecret = _config["Elasticsearch:ClientSecret"];
        var scope = _config["Elasticsearch:Scope"];
        var xsrfToken = _config["Elasticsearch:XsrfToken"];

        if (string.IsNullOrEmpty(authUrl) || string.IsNullOrEmpty(clientId) || 
            string.IsNullOrEmpty(clientSecret) || string.IsNullOrEmpty(scope))
        {
            throw new InvalidOperationException("Elasticsearch configuration is missing required values");
        }

        var client = _httpClientFactory.CreateClient();
        
        // Set up basic authentication
        var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{clientId}:{clientSecret}"));
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authValue);
        
        // Add XSRF token cookie if provided
        if (!string.IsNullOrEmpty(xsrfToken))
        {
            client.DefaultRequestHeaders.Add("Cookie", $"XSRF-TOKEN={xsrfToken}");
        }

        // Prepare form data
        var formData = new List<KeyValuePair<string, string>>
        {
            new("grant_type", "client_credentials"),
            new("scope", scope)
        };

        var formContent = new FormUrlEncodedContent(formData);
        formContent.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

        var response = await client.PostAsync(authUrl, formContent);
        response.EnsureSuccessStatusCode();

        var json = await response.Content.ReadAsStringAsync();
        var authResponse = JsonSerializer.Deserialize<ElasticsearchAuthResponse>(json);

        if (authResponse?.AccessToken == null)
        {
            throw new InvalidOperationException("Failed to obtain access token from Elasticsearch auth service");
        }

        // Cache the token (subtract 60 seconds for safety margin)
        _cachedAccessToken = authResponse.AccessToken;
        _tokenExpiry = DateTime.UtcNow.AddSeconds(authResponse.ExpiresIn - 60);

        return _cachedAccessToken;
    }

    public async Task<ElasticsearchProductResponse> GetProductsByIdsAsync(string[] productIds, string outputFormat = "json")
    {
        if (productIds == null || productIds.Length == 0)
        {
            throw new ArgumentException("Product IDs cannot be null or empty", nameof(productIds));
        }

        if (outputFormat != "json" && outputFormat != "md")
        {
            throw new ArgumentException("Output format must be 'json' or 'md'", nameof(outputFormat));
        }

        var token = await GetAccessTokenAsync();
        var apiUrl = _config["Elasticsearch:ApiUrl"];

        if (string.IsNullOrEmpty(apiUrl))
        {
            throw new InvalidOperationException("Elasticsearch API URL is not configured");
        }

        var client = _httpClientFactory.CreateClient();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var request = new ElasticsearchProductRequest
        {
            ProductIds = productIds,
            Output = outputFormat
        };

        var jsonContent = JsonSerializer.Serialize(request);
        var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        var response = await client.PostAsync(apiUrl, content);
        response.EnsureSuccessStatusCode();

        var responseJson = await response.Content.ReadAsStringAsync();
        var productResponse = JsonSerializer.Deserialize<ElasticsearchProductResponse>(responseJson);

        if (productResponse == null)
        {
            throw new InvalidOperationException("Failed to deserialize product response");
        }

        return productResponse;
    }

    public async Task<List<ElasticsearchProduct>> GetProductsListAsync(string[] productIds, string outputFormat = "json")
    {
        var response = await GetProductsByIdsAsync(productIds, outputFormat);
        
        if (!response.Success || response.Data?.Products == null)
        {
            return new List<ElasticsearchProduct>();
        }

        return response.Data.Products.ToList();
    }
}
